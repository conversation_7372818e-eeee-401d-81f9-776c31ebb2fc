@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #00418d;
  --primary-light: #335f92;
  --primary-lighter: #c3dfff;
  --secondary: #f73e5d;
  --accent: #f6c648;
  --dark: #272727;
  --dark-light: #212121;
  --light: #ffffff;
  --light-dark: #d9d9d9;
}

body {
  color: var(--dark);
  background-color: var(--light);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 224 70% 25%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Inter", sans-serif;
  }
}

@layer components {
  .blog-card {
    @apply bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300;
  }

  .blog-title {
    @apply text-xl font-bold text-[#042578] hover:text-[#00418d] transition-colors duration-200;
  }

  .blog-meta {
    @apply text-sm text-gray-500;
  }

  .btn-primary {
    @apply bg-[#00418d] text-white px-6 py-2 rounded-full hover:bg-[#042578] transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-[#c3dfff] text-[#00418d] px-6 py-2 rounded-full hover:bg-[#c4e3ff] transition-colors duration-200;
  }
}

